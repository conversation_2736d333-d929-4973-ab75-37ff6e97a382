{"name": "medical-auth-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "eslint", "postinstall": "prisma generate"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.15.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "html5-qrcode": "^2.3.8", "next": "15.5.2", "next-auth": "^4.24.11", "prisma": "^6.15.0", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24.3.1", "@types/qrcode": "^1.5.5", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4"}}