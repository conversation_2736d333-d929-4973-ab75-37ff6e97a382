services:
  - type: web
    name: medical-auth-portal
    env: node
    region: oregon
    plan: free
    buildCommand: npm install --legacy-peer-deps && npx prisma generate && npm run build
    startCommand: npm start
    healthCheckPath: /
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: medical-auth-db
          property: connectionString
      - key: NEXTAUTH_URL
        value: https://medical-auth-portal.onrender.com
      - key: NEXTAUTH_SECRET
        generateValue: true
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOOGLE_CLIENT_SECRET
        sync: false
      - key: GEMINI_API_KEY
        sync: false

databases:
  - name: medical-auth-db
    databaseName: medical_auth
    user: medical_auth_user
    region: oregon
    plan: free




